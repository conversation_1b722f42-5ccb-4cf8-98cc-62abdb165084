import React, { useState } from "react";
import { SafeAreaView } from "react-native-safe-area-context";
import { VStack } from "@/components/ui/vstack";
import { HStack } from "@/components/ui/hstack";
import { Text } from "@/components/ui/text";
import { Pressable } from "@/components/ui/pressable";
import { Icon } from "@/components/ui/icon";
import { Input, InputField, InputIcon, InputSlot } from "@/components/ui/input";
import { ScrollView } from "react-native";
import { router } from "expo-router";
import {
  ArrowLeft,
  Search,
  SlidersHorizontal,
  Users,
  Tent,
  Music,
  Drama,
  Baby,
  Plus,
} from "lucide-react-native";

interface ProgramCardProps {
  icon: React.ElementType;
  title: string;
  subtitle?: string;
  onPress?: () => void;
  isMore?: boolean;
}

const ProgramCard: React.FC<ProgramCardProps> = ({
  icon,
  title,
  subtitle,
  onPress,
  isMore = false,
}) => {
  return (
    <Pressable
      onPress={onPress}
      className="bg-white rounded-2xl p-4 flex-1 min-h-[120px] justify-center items-center border border-background-100 active:bg-background-50"
    >
      <VStack className="items-center justify-center flex-1" space="sm">
        <VStack className="bg-primary-50 rounded-full p-3 items-center justify-center">
          <Icon
            as={icon}
            size="lg"
            className={isMore ? "text-primary-500" : "text-primary-600"}
          />
        </VStack>
        <VStack className="items-center" space="xs">
          <Text className="text-sm font-dm-sans-medium text-typography-900 text-center">
            {title}
          </Text>
          {subtitle && (
            <Text className="text-xs font-dm-sans-regular text-typography-600 text-center">
              {subtitle}
            </Text>
          )}
        </VStack>
      </VStack>
    </Pressable>
  );
};

const OtherPrograms = () => {
  const [searchTerm, setSearchTerm] = useState("");

  const handleBack = () => {
    router.back();
  };

  const handleSearch = (text: string) => {
    setSearchTerm(text);
  };

  const handleProgramPress = (program: string) => {
    if (program === "More") {
      router.push({
        pathname: "/program-details",
        params: { title: "Other programs" },
      });
      return;
    }

    console.log(`Pressed: ${program}`);
    // TODO: Implement navigation for each specific program
  };

  const programs = [
    {
      icon: Users,
      title: "Adults",
      onPress: () => handleProgramPress("Adults"),
    },
    { icon: Tent, title: "Camps", onPress: () => handleProgramPress("Camps") },
    {
      icon: Music,
      title: "Dance school",
      onPress: () => handleProgramPress("Dance school"),
    },
    {
      icon: Drama,
      title: "Drama school",
      onPress: () => handleProgramPress("Drama school"),
    },
    {
      icon: Baby,
      title: "Early childhood",
      subtitle: "0 - 4 years",
      onPress: () => handleProgramPress("Early childhood"),
    },
    {
      icon: Plus,
      title: "More",
      isMore: true,
      onPress: () => handleProgramPress("More"),
    },
  ];

  return (
    <SafeAreaView className="flex-1 bg-background-50">
      <VStack className="flex-1">
        {/* Header */}
        <HStack className="px-4 py-4 bg-white items-center justify-between">
          <HStack className="items-center" space="md">
            <Pressable onPress={handleBack} className="p-1">
              <Icon as={ArrowLeft} size="lg" className="text-typography-900" />
            </Pressable>
            <Text className="text-lg font-dm-sans-bold text-typography-900">
              Other programs
            </Text>
          </HStack>
        </HStack>

        {/* Search */}
        <VStack className="px-4 pb-4 bg-white">
          <Input
            variant="outline"
            className="bg-background-50 rounded-xl"
            size="lg"
          >
            <InputSlot>
              <InputIcon as={Search} className="text-typography-400 ml-3" />
            </InputSlot>
            <InputField
              placeholder="Search"
              className="placeholder:text-typography-400"
              onChangeText={handleSearch}
              value={searchTerm}
            />
            <InputSlot>
              <Pressable className="p-2 bg-background-100 rounded-lg mr-2 border border-background-200">
                <Icon
                  as={SlidersHorizontal}
                  size="sm"
                  className="text-typography-600"
                />
              </Pressable>
            </InputSlot>
          </Input>
        </VStack>

        <ScrollView
          className="flex-1"
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{ paddingVertical: 16 }}
        >
          {/* First Grid */}
          <VStack className="px-4 mb-6" space="md">
            <HStack space="md">
              <ProgramCard {...programs[0]} />
              <ProgramCard {...programs[1]} />
            </HStack>
            <HStack space="md">
              <ProgramCard {...programs[2]} />
              <ProgramCard {...programs[3]} />
            </HStack>
            <HStack space="md">
              <ProgramCard {...programs[4]} />
              <ProgramCard {...programs[5]} />
            </HStack>
          </VStack>
        </ScrollView>
      </VStack>
    </SafeAreaView>
  );
};

export default OtherPrograms;
