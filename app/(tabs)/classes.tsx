import React, { useState } from "react";
import { VStack } from "@/components/ui/vstack";
import { SafeAreaView } from "react-native-safe-area-context";
import ClassesHeader from "@/components/screens/classes/classes-header";
import HorizontalDatePicker from "@/components/shared/horizontal-date-picker";
import ClassesTabs from "@/components/screens/classes/classes-tabs";
import { ClassCardSkeleton } from "@/components/screens/classes/class-card/class-skeleton";

import { SearchInput } from "@/components/screens/classes/classes-header/search";
import { FlatList, RefreshControl } from "react-native";

import { formatDate } from "@/data/common/common.utils";
import { ClassCard } from "@/components/screens/classes/class-card/class-card";
import { AppointmentCard } from "@/components/screens/appointments/appointment-card";

import { EmptyState } from "@/components/screens/classes/empty-state";
import { EmptySearchIcon } from "@/components/shared/icon/empty-search";

import { matchSorter } from "match-sorter";
import { But<PERSON>, ButtonText } from "@/components/ui/button";
import { useClassesQuery } from "@/data/screens/classes/queries/useClassesQuery";
import { useAppointmentsQuery } from "@/data/screens/appointments/queries/useAppointmentsQuery";
import { ClassDetailsResponse } from "@/data/screens/classes/types";
import { AppointmentType } from "@/data/screens/appointments/types";

const RenderEmptyState = ({
  isLoading,
  isEmpty,
  searchTerm,
  setSearchTerm,
}: {
  isLoading: boolean;
  isEmpty: boolean;
  searchTerm?: string;
  setSearchTerm: (text: string) => void;
}) => {
  if (isLoading) {
    return (
      <FlatList
        data={skeletonData}
        renderItem={() => <ClassCardSkeleton />}
        keyExtractor={(item) => String(item.id)}
        showsVerticalScrollIndicator={false}
      />
    );
  }

  if (searchTerm && isEmpty) {
    return (
      <EmptyState
        subtitle="No class matched the search criteria you entered"
        title="No result found"
        icon={<EmptySearchIcon />}
        action={
          <Button
            onPress={() => setSearchTerm("")}
            variant="outline"
            size="sm"
            className="rounded-full mt-4"
          >
            <ButtonText>Clear search</ButtonText>
          </Button>
        }
      />
    );
  }

  if (isEmpty) {
    return <EmptyState />;
  }
};

const skeletonData = Array.from({ length: 5 }, (_, index) => ({ id: index }));

export const Classes = () => {
  const [selectedTab, setSelectedTab] = useState<"classes" | "appointment">(
    "classes"
  );
  const [selectedDate, setSelectedDate] = useState(new Date());

  const [searchTerm, setSearchTerm] = useState("");

  const {
    data: classesData = [],
    isLoading: classesLoading,
    refetch: refetchClasses,
  } = useClassesQuery({
    date: formatDate(selectedDate),
  });

  const {
    data: appointmentsData = [],
    isLoading: appointmentsLoading,
    refetch: refetchAppointments,
  } = useAppointmentsQuery({
    date: formatDate(selectedDate),
  });

  // Determine which data and loading state to use based on selected tab
  const data = selectedTab === "classes" ? classesData : appointmentsData;
  const isLoading =
    selectedTab === "classes" ? classesLoading : appointmentsLoading;
  const refetch =
    selectedTab === "classes" ? refetchClasses : refetchAppointments;

  const filteredData = searchTerm
    ? selectedTab === "classes"
      ? matchSorter(classesData, searchTerm, {
          keys: [
            "name",
            "room_name",
            "gym_name",
            "instructor_first_name",
            "instructor_last_name",
            "start_time",
          ],
        })
      : matchSorter(appointmentsData, searchTerm, {
          keys: ["name", "gym_name", "room_name", "reservations_count"],
        })
    : data;

  return (
    <SafeAreaView className="flex-1 bg-white">
      <VStack className="flex-1">
        <ClassesHeader />
        <ClassesTabs selectedTab={selectedTab} onTabSelect={setSelectedTab} />
        <VStack space="md" className="pb-6">
          <HorizontalDatePicker
            selectedDate={selectedDate}
            onDateSelect={setSelectedDate}
          />
          <SearchInput onSearch={setSearchTerm} searchTerm={searchTerm} />

          <VStack space="sm" className="px-4 ">
            {selectedTab === "classes" ? (
              // Classes Tab Content
              isLoading || !filteredData.length ? (
                <RenderEmptyState
                  setSearchTerm={setSearchTerm}
                  isLoading={isLoading}
                  isEmpty={!Boolean(filteredData?.length)}
                  searchTerm={searchTerm}
                />
              ) : (
                <FlatList
                  refreshControl={
                    <RefreshControl
                      refreshing={isLoading}
                      onRefresh={() => refetch()}
                    />
                  }
                  data={filteredData as ClassDetailsResponse[]}
                  renderItem={({ item }) => (
                    <ClassCard
                      key={item.id}
                      {...item}
                      selected_date={formatDate(selectedDate)}
                    />
                  )}
                  keyExtractor={(item) => String(item.id)}
                  showsVerticalScrollIndicator={false}
                />
              )
            ) : // Appointments Tab Content
            isLoading || !filteredData.length ? (
              <RenderEmptyState
                setSearchTerm={setSearchTerm}
                isLoading={isLoading}
                isEmpty={!Boolean(filteredData?.length)}
                searchTerm={searchTerm}
              />
            ) : (
              <FlatList
                refreshControl={
                  <RefreshControl
                    refreshing={isLoading}
                    onRefresh={() => refetch()}
                  />
                }
                data={filteredData as AppointmentType[]}
                renderItem={({ item }) => (
                  <AppointmentCard key={item.id} {...item} />
                )}
                keyExtractor={(item) => String(item.id)}
                showsVerticalScrollIndicator={false}
              />
            )}
          </VStack>
        </VStack>
      </VStack>
    </SafeAreaView>
  );
};

export default Classes;
