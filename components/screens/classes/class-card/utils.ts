import { ClassDetailsResponse } from "@/data/screens/classes/types";

export const getButtonStatus = (classData: ClassDetailsResponse) => {
  if (classData.current_user_reservation) return "cancel_reservation";
  // if (classData.user_has_reservation) return "cancel_reservation";
  // if (classData.reservation_status === "reserved") return "reserved";
  // if (classData.reservation_status === "waitlisted") return "waitlist";
  // if (classData.is_full && classData.allows_walk_in) return "walk_in_available";
  if (classData.is_full) return "class_full";
  return "available";
};
