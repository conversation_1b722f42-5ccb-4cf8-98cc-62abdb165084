export interface AppointmentType {
  id: number;
  gym_name: string;
  name: string;
  reservations_count: number;
  room_name: string;
}

export interface Trainer {
  id: number;
  firstName: string;
  lastName: string;
  fullName: string;
  image: string;
  experience: string;
  specialties: string[];
  tags: string[];
  description: string;
  rating?: number;
  reviewCount?: number;
  isAvailable?: boolean;
}

export interface AppointmentCard {
  id: number;
  data: AppointmentType;
  trainers: Trainer[];
}
